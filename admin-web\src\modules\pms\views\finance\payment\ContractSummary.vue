<script lang="ts" name="pms-contract-summary" setup>
import { useCrud, useTable } from '@cool-vue-p/crud'
import { DocumentDelete } from '@element-plus/icons-vue'
import dayjs from 'dayjs'
import { ElMessage, ElMessageBox } from 'element-plus'
import moment from 'moment/moment'
import { ref } from 'vue'
import { getDictLabel } from '/$/pims/utils'
import { useTableOps } from '/$/pms/hooks/table-ops'
import { useCool } from '/@/cool'
import { downloadBlob } from '/@/cool/utils'

const supplierList = ref<any[]>([])
const materialOptions = ref<any[]>([])
const queryLoading = ref<boolean>(false)
const tableLoading = ref<boolean>(false)
const { service } = useCool()

// 添加导出相关的加载状态变量
const exportLoading = ref<boolean>(false)
const batchExportLoading = ref<boolean>(false)
const summaryExportLoading = ref<boolean>(false)

const queryParams = ref<any>({
  date: [dayjs().startOf('month').toDate(), dayjs().endOf('month').toDate()],
  keyWord: '',
  status: undefined,
})
const opButtons = ref({
  'slot-btn-export': {
    width: 220,
    show: true,
  },
  'slot-btn-updateStatus': {
    width: 220,
    show: true,
  },
})

// 获取物料列表
async function getMaterial() {
  try {
    const res = await service.pms.material.request({
      url: '/list',
      method: 'POST',
    })
    materialOptions.value = res.map((e: any) => {
      return {
        ...e,
        value: e.id,
        label: `${e.name} / ${e.code}`,
      }
    })
  }
  catch (e: any) {
    console.error(e)
  }
}

// 获取供应商列表
async function getSupplier() {
  try {
    supplierList.value = await service.pms.supplier.request({
      url: '/list',
      method: 'POST',
    })
  }
  catch (e: any) {
    console.error(e)
  }
}
watchEffect(() => {
  getMaterial()
  getSupplier()
})
const { getOpWidth } = useTableOps(opButtons as any)
const opWidth = ref(getOpWidth() + 120)
// const opIsHidden = ref(getOpIsHidden())

// cl-table 配置
const Table = useTable({
  columns: [
    {
      label: '展开',
      prop: 'contract',
      type: 'expand',
      width: 60,
    },
    { label: '供应商名', prop: 'name', minWidth: 260 },
    { label: '联系人', prop: 'contact', minWidth: 160 },
    // { label: '电话', prop: 'phone', width: 260 },
    { label: '当前合同总金额', prop: 'contractTotalAmount', minWidth: 260 },
    { label: '当前工时扣款金额', prop: 'manHourDeductAmount', minWidth: 260 },
    { label: '当前物料扣款金额', prop: 'materialDeductAmount', minWidth: 260 },
    {
      label: '当前扣款总金额',
      minWidth: 160,
      formatter(row) {
        return row.manHourDeductAmount + row.materialDeductAmount
      },
    },
    {
      label: '当前补退货金额',
      minWidth: 160,
      formatter(row) {
        if (!row.return_policy || row.return_policy.length === 0)
          return 0
        return row.return_policy
          .reduce((sum: number, item: any) => sum + (item.quantity * item.unitPrice), 0)
          .toFixed(2)
      },
    },
    {
      label: '应付金额',
      minWidth: 160,
      formatter(row) {
        const returnPolicyAmount = row.return_policy
          ? row.return_policy.reduce((sum: number, item: any) => sum + (item.quantity * item.unitPrice), 0)
          : 0
        return (
          row.contractTotalAmount
          - row.manHourDeductAmount
          - row.materialDeductAmount
          + returnPolicyAmount
        ).toFixed(2)
      },
    },
    {
      type: 'op',
      label: '操作',
      width: 320,
      buttons: Object.keys(opButtons.value) as any,
    },
  ],
})

// cl-crud 配置
const Crud = useCrud(
  {
    dict: {
      api: { page: 'financePaymentPage' },
    },
    service: service.pms.payment,

    async onRefresh(params, { next, done, render }) {
      tableLoading.value = true
      done()
      formatQueryParams(params)

      // 确保请求中包含需要的关联数据
      params.with = ['contract', 'man_hour_deduct', 'material_deduct', 'return_policy']

      try {
        const { list, pagination } = await next(params)
        // 渲染数据
        render(list, pagination)
      }
      catch (error) {
        console.error('加载数据失败', error)
      }
      finally {
        tableLoading.value = false
      }
    },
  },
  (app) => {
    app.refresh()
  },
)
// 格式化查询参数
function formatQueryParams(params: any) {
  if (queryParams.value.date && queryParams.value.date.length > 0) {
    const start = moment(queryParams.value.date[0]).startOf('day').format('YYYY-MM-DD HH:mm:ss')
    const end = moment(queryParams.value.date[1]).endOf('day').format('YYYY-MM-DD HH:mm:ss')
    // const start = dayjs(queryParams.value.date[0]).format('YYYY-MM-DD')
    // const end = dayjs(queryParams.value.date[1]).format('YYYY-MM-DD')
    params.date = `${start},${end}`
  }
  if (queryParams.value.keyWord) {
    params.keyWord = queryParams.value.keyWord
  }
  if (queryParams.value.supplierId) {
    params.supplierId = queryParams.value.supplierId
  }
  return params
}

// 清空查询条件
function refresh() {
  queryParams.value.keyWord = ''
  queryParams.value.supplierId = undefined
  queryParams.value.date = [
    dayjs().startOf('month').toDate(),
    dayjs().endOf('month').toDate(),
  ]
  if (Crud?.value?.params) {
    Crud.value.params.page = 1
    Crud.value.params.size = 20
  }
  Crud?.value?.refresh()
}

async function doQuery() {
  try {
    if (Crud?.value?.params?.page) {
      Crud.value.params.page = 1
    }
    queryLoading.value = true
    tableLoading.value = true
    Crud?.value?.refresh()
  }
  catch (e: any) {
    console.error(e)
  }
  finally {
    queryLoading.value = false
  }
}

function dateChange(val: any) {
  val ? doQuery() : refresh()
}
const tableExpandRowKeys = ref<number[]>([])
// 行点击展开
function onRowClick(row: any, column: any) {
  // 获取row的key
  if (column?.type === 'expand' || column?.type === 'op')
    return
  Table.value?.toggleRowExpansion(row)
}

// 导出数据
function handleExport(row: any) {
  // 处理导出功能
  exportLoading.value = true
  const params = {
    url: '/export',
    method: 'POST',
    responseType: 'blob',
    data: { finance_payment_vo: row },
  }
  service.pms.payment
    .request(params)
    .then((res: any) => {
      if (downloadBlob(res))
        ElMessage.success('导出成功')
      Crud.value?.refresh()
    })
    .catch((err: any) => {
      // 提示错误消息
      ElMessage.error(err.message || '导出失败')
    })
    .finally(() => {
      exportLoading.value = false
    })
}

// 确认对话框后执行导出操作的辅助函数
function _confirmExport(exportFn: () => void) {
  ElMessage.closeAll() // 关闭所有提示

  ElMessageBox.confirm(
    '数据量过大，请耐心等待导出。是否继续？',
    '导出提示',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    },
  )
    .then(() => {
      exportFn() // 确认后执行导出操作
    })
    .catch(() => {
      // 用户点击取消，不执行导出
      ElMessage.info('已取消导出')
    })
}

function summaryExport() {
  // 显示确认对话框，提醒用户数据量大需要耐心等待
  ElMessageBox.confirm(
    '数据量过大，请耐心等待导出。是否继续？',
    '导出提示',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    },
  )
    .then(() => {
      // 用户确认后继续执行导出
      const param: any = {}
      formatQueryParams(param)
      // 确保请求中包含需要的关联数据
      param.with = ['contract', 'man_hour_deduct', 'material_deduct', 'return_policy']
      param.page = 1
      param.size = 1000000
      // 处理导出功能
      summaryExportLoading.value = true
      const params = {
        url: '/summaryExport',
        method: 'POST',
        responseType: 'blob',
        data: param,
      }
      service.pms.payment
        .request(params)
        .then((res: any) => {
          if (downloadBlob(res))
            ElMessage.success('导出成功')
          Crud.value?.refresh()
        })
        .catch((err: any) => {
          // 提示错误消息
          ElMessage.error(err.message || '导出失败')
        })
        .finally(() => {
          summaryExportLoading.value = false
        })
    })
    .catch(() => {
      // 用户点击取消，不执行导出
      ElMessage.info('已取消导出')
    })
}

// 批量导出
function batchExport() {
  const param: any = {}
  formatQueryParams(param)
  // 确保请求中包含需要的关联数据
  param.with = ['contract', 'man_hour_deduct', 'material_deduct', 'return_policy']
  param.page = 1
  param.size = 1000000
  // 处理导出功能
  batchExportLoading.value = true
  const params = {
    url: '/batchExport',
    method: 'POST',
    responseType: 'blob',
    data: param,
  }
  service.pms.payment
    .request(params)
    .then((res: any) => {
      if (downloadBlob(res))
        ElMessage.success('导出成功')
      Crud.value?.refresh()
    })
    .catch((err: any) => {
      // 提示错误消息
      ElMessage.error(err.message || '导出失败')
    })
    .finally(() => {
      batchExportLoading.value = false
    })
}

function updateStatus(row: any) {
  console.log('row====================================', row)
}
</script>

<template>
  <cl-crud ref="Crud">
    <el-row>
      <el-button @click="refresh">
        刷新
      </el-button>
      <cl-flex1 />

      <div style="margin-right: 20px">
        <el-date-picker
          v-model="queryParams.date"
          type="daterange"
          clearable
          range-separator="~"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          @change="dateChange"
        />
      </div>
      <div style="margin-right: 20px; width: 300px">
        <el-select
          v-model="queryParams.supplierId"
          placeholder="请选择供应商"
          filterable
          @change="doQuery"
        >
          <el-option label="全部" value="" />
          <el-option
            v-for="item in supplierList"
            :key="item.id"
            :label="item.supplierName"
            :value="item.id"
          />
        </el-select>
      </div>
      <el-input
        v-model="queryParams.keyWord"
        placeholder="请输入物料编号"
        style="width: 200px"
        clearable
        @clear="refresh"
        @keyup.enter="doQuery"
      />
      <el-button
        type="primary"
        mx="10px"
        :loading="queryLoading"
        @click="doQuery"
      >
        搜索
      </el-button>
      <el-button
        type="warning"
        mx="10px"
        :loading="batchExportLoading"
        @click="batchExport"
      >
        批量导出
      </el-button>
      <el-button
        type="success"
        mx="10px"
        :loading="summaryExportLoading"
        @click="summaryExport"
      >
        导出汇总
      </el-button>
    </el-row>

    <el-row>
      <!-- 数据表格 -->
      <cl-table
        ref="Table"
        v-loading="tableLoading"
        row-key="id"
        :expand-row-keys="tableExpandRowKeys"
        class="table-row-pointer"
        @row-click="onRowClick"
      >
        <template #slot-btn-export="{ scope }">
          <el-button
            v-permission="service.pms.payment.permission.export"
            type="success"
            :loading="exportLoading"
            @click="handleExport(scope.row)"
          >
            导出
          </el-button>
        </template>
        <template #slot-btn-updateStatus="{ scope }">
          <el-button
            v-permission="service.pms.payment.permission.export"
            type="primary"
            :disabled="scope.row.contract == null"
            :loading="exportLoading"
            @click="updateStatus(scope.row)"
          >
            更新付款状态
          </el-button>
        </template>
        <template #column-contract="{ scope }">
          <!-- 缺省显示：当没有任何数据时显示 -->
          <div
            v-if="
              (!scope.row.contract || scope.row.contract.length === 0)
                && (!scope.row.man_hour_deduct
                  || scope.row.man_hour_deduct.length === 0)
                && (!scope.row.material_deduct
                  || scope.row.material_deduct.length === 0)
                && (!scope.row.return_policy
                  || scope.row.return_policy.length === 0)
            "
            class="no-data-container"
          >
            <el-empty description="暂无合同数据" :image-size="120">
              <template #image>
                <el-icon class="no-data-icon">
                  <DocumentDelete />
                </el-icon>
              </template>
              <template #description>
                <p class="no-data-text">
                  该供应商在当前筛选条件下暂无合同数据
                </p>
                <p class="no-data-subtext">
                  您可以尝试调整日期范围或搜索条件
                </p>
              </template>
            </el-empty>
          </div>

          <el-table
            v-if="scope.row.contract && scope.row.contract.length > 0"
            :data="scope.row.contract"
            style="width: 100%"
            border
          >
            <!--            <el-table-column label="创建时间" width="220" align="center"> -->
            <!--              <template #default="slotScope"> -->
            <!--                <span>{{ slotScope.row.createTime }}</span> -->
            <!--              </template> -->
            <!--            </el-table-column> -->
            <el-table-column label="PO号" align="center" min-width="220">
              <template #default="slotScope">
                <span>{{ slotScope.row.po }}</span>
              </template>
            </el-table-column>
            <el-table-column label="物料编码" align="center" min-width="220">
              <template #default="slotScope">
                <span>{{ slotScope.row.material.code }}</span>
              </template>
            </el-table-column>
            <el-table-column label="物料名称" align="center" min-width="120">
              <template #default="slotScope">
                <span>{{ slotScope.row.material.name }}</span>
              </template>
            </el-table-column>
            <el-table-column label="合同单价" min-width="120" align="center">
              <template #default="slotScope">
                <span>{{ slotScope.row.unitPrice }}</span>
              </template>
            </el-table-column>
            <el-table-column label="合同数量" min-width="120" align="center">
              <template #default="slotScope">
                <span>{{ slotScope.row.quantity }}</span>
              </template>
            </el-table-column>
            <el-table-column label="已收数量" min-width="120" align="center">
              <template #default="slotScope">
                <span>{{ slotScope.row.receivedQuantity }}</span>
              </template>
            </el-table-column>
            <!--            <el-table-column label="在途数量" min-width="120" align="center"> -->
            <!--              <template #default="slotScope"> -->
            <!--                <span>{{ slotScope.row.expectedQuantity }}</span> -->
            <!--              </template> -->
            <!--            </el-table-column> -->
            <el-table-column label="合同金额" min-width="120" align="center">
              <template #default="slotScope">
                <span>{{ slotScope.row.subtotal }}</span>
              </template>
            </el-table-column>
            <el-table-column label="本期入库数量" min-width="120" align="center">
              <template #default="slotScope">
                <span>{{ slotScope.row.curInboundQuantity }}</span>
              </template>
            </el-table-column>
            <el-table-column label="本期出库数量" min-width="120" align="center">
              <template #default="slotScope">
                <span>{{ slotScope.row.curOutboundQuantity }}</span>
              </template>
            </el-table-column>
          </el-table>

          <!-- 合同金额统计 -->
          <div
            v-if="scope.row.contract && scope.row.contract.length > 0"
            class="deduct-section contract-section"
          >
            <h3>合同统计信息</h3>
            <div class="deduct-summary contract-summary">
              <div class="summary-row">
                <div class="summary-item">
                  <span class="summary-label">合同总数量：</span>
                  <span class="summary-value">{{
                    scope.row.contract
                      .reduce(
                        (sum: number, item: any) => sum + item.quantity,
                        0,
                      )
                      .toFixed(2)
                  }}</span>
                </div>
                <div class="summary-item">
                  <span class="summary-label">已收总数量：</span>
                  <span class="summary-value">{{
                    scope.row.contract
                      .reduce(
                        (sum: number, item: any) => sum + item.receivedQuantity,
                        0,
                      )
                      .toFixed(2)
                  }}</span>
                </div>
                <div class="summary-item">
                  <span class="summary-label">在途总数量：</span>
                  <span class="summary-value">{{
                    scope.row.contract
                      .reduce(
                        (sum: number, item: any) => sum + item.expectedQuantity,
                        0,
                      )
                      .toFixed(2)
                  }}</span>
                </div>
              </div>
              <div class="summary-row">
                <div class="summary-item">
                  <span class="summary-label">本期入库总数量：</span>
                  <span class="summary-value">{{
                    scope.row.contract
                      .reduce(
                        (sum: number, item: any) =>
                          sum + item.curInboundQuantity,
                        0,
                      )
                      .toFixed(2)
                  }}</span>
                </div>
                <div class="summary-item">
                  <span class="summary-label">本期出库总数量：</span>
                  <span class="summary-value">{{
                    scope.row.contract
                      .reduce(
                        (sum: number, item: any) =>
                          sum + item.curOutboundQuantity,
                        0,
                      )
                      .toFixed(2)
                  }}</span>
                </div>
                <div class="summary-item">
                  <span class="summary-label">本期合同总金额：</span>
                  <span
                    class="summary-value"
                    style="color: red; font-weight: bold"
                  >{{ scope.row.contractTotalAmount.toFixed(2) }} 元</span>
                </div>
              </div>
            </div>
          </div>

          <!-- 工时扣款信息 -->
          <div
            v-if="
              scope.row.man_hour_deduct && scope.row.man_hour_deduct.length > 0
            "
            class="deduct-section"
          >
            <h3>工时扣款信息</h3>
            <el-table
              :data="scope.row.man_hour_deduct"
              style="width: 100%"
              border
            >
              <el-table-column label="事故时间" min-width="180" align="center">
                <template #default="slotScope">
                  <span>{{ slotScope.row.accident_time }}</span>
                </template>
              </el-table-column>
              <el-table-column label="扣款号" min-width="220" align="center">
                <template #default="slotScope">
                  <span>{{ slotScope.row.no }}</span>
                </template>
              </el-table-column>
              <el-table-column label="不良物料名称" min-width="220" align="center">
                <template #default="slotScope">
                  <span>{{ slotScope.row.badMaterialName }}</span>
                </template>
              </el-table-column>
              <el-table-column label="凭证" min-width="120" align="center">
                <template #default="slotScope">
                  <el-image
                    v-if="slotScope.row.voucher"
                    :src="slotScope.row.voucher"
                    :preview-src-list="[slotScope.row.voucher]"
                    fit="cover"
                    style="
                      width: 80px;
                      height: 80px;
                      cursor: zoom-in;
                      border-radius: 4px;
                      box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
                      transition: transform 0.3s ease;
                    "
                    :preview-teleported="true"
                    @mouseenter="$event.target.style.transform = 'scale(1.05)'"
                    @mouseleave="$event.target.style.transform = 'scale(1)'"
                  />
                  <span v-else>无</span>
                </template>
              </el-table-column>
              <el-table-column label="人数" min-width="120" align="center">
                <template #default="slotScope">
                  <span>{{ slotScope.row.number_of_people }}</span>
                </template>
              </el-table-column>
              <el-table-column label="工时总计" min-width="120" align="center">
                <template #default="slotScope">
                  <span>{{ slotScope.row.man_hour }}</span>
                </template>
              </el-table-column>
              <el-table-column
                label="人工费率(元/h)"
                width="220"
                align="center"
              >
                <template #default="slotScope">
                  <span>{{ slotScope.row.labor_rate }}</span>
                </template>
              </el-table-column>
              <el-table-column label="人均工时" min-width="120" align="center">
                <template #default="slotScope">
                  <span>{{
                    (
                      slotScope.row.man_hour / slotScope.row.number_of_people
                    ).toFixed(2)
                  }}</span>
                </template>
              </el-table-column>
              <el-table-column label="扣款金额" min-width="120" align="center">
                <template #default="slotScope">
                  <span>{{ slotScope.row.total_amount }}</span>
                </template>
              </el-table-column>
              <el-table-column label="描述" align="center">
                <template #default="slotScope">
                  <el-tooltip
                    :content="slotScope.row.description"
                    placement="top"
                    :show-after="200"
                    :enterable="false"
                  >
                    <span class="ellipsis-text">{{
                      slotScope.row.description
                    }}</span>
                  </el-tooltip>
                </template>
              </el-table-column>
            </el-table>

            <!-- 工时扣款总计 -->
            <div class="deduct-summary">
              <span>工时扣款总金额：</span>
              <span class="deduct-amount">{{ scope.row.manHourDeductAmount.toFixed(2) }} 元</span>
            </div>
          </div>

          <!-- 物料扣款信息 -->
          <div
            v-if="
              scope.row.material_deduct && scope.row.material_deduct.length > 0
            "
            class="deduct-section"
          >
            <h3>物料扣款信息</h3>
            <el-table
              :data="scope.row.material_deduct"
              style="width: 100%"
              border
            >
              <el-table-column label="事故时间" min-width="180" align="center">
                <template #default="slotScope">
                  <span>{{ slotScope.row.accident_time }}</span>
                </template>
              </el-table-column>
              <el-table-column label="扣款号" min-width="220" align="center">
                <template #default="slotScope">
                  <span>{{ slotScope.row.no }}</span>
                </template>
              </el-table-column>
              <el-table-column label="不良物料名称" min-width="220" align="center">
                <template #default="slotScope">
                  <span>{{ slotScope.row.badMaterialName }}</span>
                </template>
              </el-table-column>
              <el-table-column label="报废物料名称" min-width="220" align="center">
                <template #default="slotScope">
                  <span>{{ slotScope.row.materialName }}</span>
                </template>
              </el-table-column>
              <el-table-column label="凭证" min-width="120" align="center">
                <template #default="slotScope">
                  <el-image
                    v-if="slotScope.row.voucher"
                    :src="slotScope.row.voucher"
                    :preview-src-list="[slotScope.row.voucher]"
                    fit="cover"
                    style="
                      width: 80px;
                      height: 80px;
                      cursor: zoom-in;
                      border-radius: 4px;
                      box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
                      transition: transform 0.3s ease;
                    "
                    :preview-teleported="true"
                    @mouseenter="$event.target.style.transform = 'scale(1.05)'"
                    @mouseleave="$event.target.style.transform = 'scale(1)'"
                  />
                  <span v-else>无</span>
                </template>
              </el-table-column>
              <el-table-column label="数量" min-width="120" align="center">
                <template #default="slotScope">
                  <span>{{ slotScope.row.quantity }}</span>
                </template>
              </el-table-column>

              <el-table-column label="扣款单价" min-width="120" align="center">
                <template #default="slotScope">
                  <span>{{ slotScope.row.unitPrice }}</span>
                </template>
              </el-table-column>

              <el-table-column label="扣款金额" min-width="120" align="center">
                <template #default="slotScope">
                  <span>{{ slotScope.row.total_amount }}</span>
                </template>
              </el-table-column>
              <el-table-column label="描述" align="center">
                <template #default="slotScope">
                  <el-tooltip
                    :content="slotScope.row.description"
                    placement="top"
                    :show-after="200"
                    :enterable="false"
                  >
                    <span class="ellipsis-text">{{
                      slotScope.row.description
                    }}</span>
                  </el-tooltip>
                </template>
              </el-table-column>
            </el-table>

            <!-- 物料扣款总计 -->
            <div class="deduct-summary">
              <span>物料扣款总金额：</span>
              <span class="deduct-amount">{{ scope.row.materialDeductAmount.toFixed(2) }} 元</span>
            </div>
          </div>

          <!-- 补退货信息 -->
          <div
            v-if="
              scope.row.return_policy && scope.row.return_policy.length > 0
            "
            class="deduct-section return-policy-section"
          >
            <h3>补退货信息</h3>
            <el-table
              :data="scope.row.return_policy"
              style="width: 100%"
              border
            >
              <el-table-column label="物料名称" align="center" min-width="180">
                <template #default="slotScope">
                  <span>{{ materialOptions.find(item => item.id === slotScope.row.materialId)?.label }}</span>
                </template>
              </el-table-column>
              <el-table-column label="仓位" min-width="180" align="center">
                <template #default="slotScope">
                  <span>{{ slotScope.row.address }}</span>
                </template>
              </el-table-column>
              <el-table-column label="合同ID" min-width="220" align="center">
                <template #default="slotScope">
                  <span>{{ slotScope.row.contractId }}</span>
                </template>
              </el-table-column>
              <el-table-column label="入库ID" min-width="220" align="center">
                <template #default="slotScope">
                  <span>{{ slotScope.row.inboundId }}</span>
                </template>
              </el-table-column>
              <el-table-column label="关键字" min-width="120" align="center">
                <template #default="slotScope">
                  <el-tag :type="slotScope.row.inbound_outbound_key === 0 ? 'success' : 'warning'">
                    {{ slotScope.row.inbound_outbound_key === 0 ? '无' : getDictLabel('inbound_outbound_key', slotScope.row.inbound_outbound_key) }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column label="PO号" min-width="180" align="center">
                <template #default="slotScope">
                  <span>{{ slotScope.row.po || '-' }}</span>
                </template>
              </el-table-column>
              <el-table-column label="数量" min-width="120" align="center">
                <template #default="slotScope">
                  <span>{{ slotScope.row.quantity }}</span>
                </template>
              </el-table-column>
              <el-table-column label="单价" min-width="120" align="center">
                <template #default="slotScope">
                  <span>{{ slotScope.row.unitPrice }}</span>
                </template>
              </el-table-column>
              <el-table-column label="金额" min-width="120" align="center">
                <template #default="slotScope">
                  <span>{{ (slotScope.row.quantity * slotScope.row.unitPrice).toFixed(2) }}</span>
                </template>
              </el-table-column>
            </el-table>

            <!-- 补退货总计 -->
            <div class="deduct-summary">
              <span>补退货总数量：</span>
              <span class="deduct-amount">{{
                scope.row.return_policy
                  .reduce((sum: number, item: any) => sum + item.quantity, 0)
                  .toFixed(2)
              }}</span>
            </div>
            <div class="deduct-summary">
              <span>补退货总金额：</span>
              <span class="deduct-amount">{{
                scope.row.return_policy
                  .reduce((sum: number, item: any) => sum + (item.quantity * item.unitPrice), 0)
                  .toFixed(2)
              }} 元</span>
            </div>
          </div>

          <!-- 总金额汇总 -->
          <div
            v-if="
              (scope.row.contract && scope.row.contract.length > 0)
                || (scope.row.man_hour_deduct
                  && scope.row.man_hour_deduct.length > 0)
                || (scope.row.material_deduct
                  && scope.row.material_deduct.length > 0)
                || (scope.row.return_policy
                  && scope.row.return_policy.length > 0)
            "
            class="deduct-section total-section"
          >
            <div class="deduct-summary total">
              <span>本期合同总金额：</span>
              <span class="deduct-amount">{{
                scope.row.contractTotalAmount.toFixed(2)
              }} 元</span>
            </div>
            <div class="deduct-summary total">
              <span>扣款总金额：</span>
              <span class="deduct-amount">{{
                (
                  scope.row.manHourDeductAmount
                  + scope.row.materialDeductAmount
                ).toFixed(2)
              }}
                元</span>
            </div>
            <div
              v-if="scope.row.return_policy && scope.row.return_policy.length > 0"
              class="deduct-summary total"
            >
              <span>补退货总金额：</span>
              <span class="deduct-amount">{{
                scope.row.return_policy
                  .reduce((sum: number, item: any) => sum + (item.quantity * item.unitPrice), 0)
                  .toFixed(2)
              }} 元</span>
            </div>
            <div class="deduct-summary total">
              <span>应付金额：</span>
              <span class="deduct-amount">{{
                (
                  scope.row.contractTotalAmount
                  - scope.row.manHourDeductAmount
                  - scope.row.materialDeductAmount
                  + (scope.row.return_policy
                    ? scope.row.return_policy.reduce((sum: number, item: any) => sum + (item.quantity * item.unitPrice), 0)
                    : 0)
                ).toFixed(2)
              }}
                元</span>
            </div>
          </div>
        </template>
      </cl-table>
    </el-row>
    <el-row>
      <cl-flex1 />
      <!-- 分页控件 -->
      <cl-pagination />
    </el-row>
  </cl-crud>
</template>

<style lang="scss">
.material-deduct-form {
  .cl-form__items {
    .el-row {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      .full-line {
        grid-column: 1 / -1;
      }
    }

    // 让input-number的提示语靠左显示
    .el-input-number {
      :deep(.el-input__wrapper) {
        padding-left: 11px;
        input {
          text-align: left;
        }
      }
    }
  }
}
.cell .holiday {
  position: absolute;
  width: 6px;
  height: 6px;
  background: var(--el-color-danger);
  border-radius: 50%;
  bottom: 0px;
  left: 50%;
  transform: translateX(-50%);
}
.table-row-pointer {
  .el-table__row {
    cursor: pointer;
  }

  .el-table__expanded-cell {
    .el-table__row {
      cursor: default;
    }
  }
}

.deduct-section {
  margin-top: 20px;
  border-top: 1px solid #e6e6e6;
  padding-top: 10px;

  h3 {
    margin: 10px 0;
    color: #606266;
    font-weight: 600;
    font-size: 16px;
    position: relative;
    padding-left: 12px;

    &::before {
      content: '';
      position: absolute;
      left: 0;
      top: 50%;
      transform: '' anslateY(-50%);
      width: 4px;
      height: 16px;
      background-color: #409eff;
      border-radius: 2px;
    }
  }

  .el-table {
    margin-bottom: 15px;
  }

  .deduct-summary {
    text-align: right;
    padding: 5px 20px;
    font-size: 14px;

    .deduct-amount {
      color: #f56c6c;
      font-weight: bold;
      font-size: 16px;
      margin-left: 5px;
    }

    &.total {
      font-size: 16px;

      .deduct-amount {
        font-size: 18px;
      }
    }
  }
}

.total-section {
  background-color: #f5f7fa;
  padding: 10px;
  border-radius: 4px;
  margin-top: 20px;
}

.contract-section {
  background-color: #f0f9ff;
  padding: 5px;
  border-radius: 4px;
  margin-top: 10px;
  border-top: none;
}

.return-policy-section {
  background-color: #f7f7ff;
  padding: 5px;
  border-radius: 4px;
  margin-top: 15px;
  border-left: 3px solid #6366f1;
}

.contract-summary {
  text-align: left;
  padding: 5px 10px;

  .summary-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 8px;
  }

  .summary-item {
    flex: 1;
    padding: 0 10px;

    .summary-label {
      font-size: 14px;
      color: #606266;
    }

    .summary-value {
      font-size: 15px;
      font-weight: 500;
      color: #303133;
      margin-left: 5px;
    }
  }
}

.ellipsis-text {
  display: inline-block;
  width: 100%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
/* 优化图片查看器样式 */
:deep(.el-image-viewer__wrapper) {
  background-color: rgba(0, 0, 0, 0.85);
}

:deep(.el-image-viewer__close) {
  color: #fff;
  font-size: 30px;
  background-color: rgba(0, 0, 0, 0.4);
  border-radius: 50%;
  width: 40px;
  height: 40px;
  line-height: 40px;
  right: 40px;
  top: 40px;
}

:deep(.el-image-viewer__canvas img) {
  max-width: 80%;
  max-height: 80%;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.5);
  border-radius: 4px;
}

:deep(.el-image-viewer__actions) {
  padding: 15px;
  background-color: rgba(0, 0, 0, 0.5);
  border-radius: 30px;
}

:deep(.el-image-viewer__prev),
:deep(.el-image-viewer__next) {
  font-size: 40px;
  color: #fff;
  background-color: rgba(0, 0, 0, 0.3);
  border-radius: 50%;
  width: 50px;
  height: 50px;
}

/* 无数据显示样式 */
.no-data-container {
  padding: 30px;
  background-color: #f9f9f9;
  border-radius: 8px;
  margin-bottom: 20px;

  .no-data-icon {
    font-size: 80px;
    color: #909399;
  }

  .no-data-text {
    font-size: 16px;
    color: #606266;
    margin-bottom: 5px;
  }

  .no-data-subtext {
    font-size: 14px;
    color: #909399;
    margin: 0;
  }
}
</style>
